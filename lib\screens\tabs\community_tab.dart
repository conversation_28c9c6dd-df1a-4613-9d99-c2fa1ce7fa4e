import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CommunityTab extends StatefulWidget {
  const CommunityTab({super.key});

  @override
  State<CommunityTab> createState() => _CommunityTabState();
}

class _CommunityTabState extends State<CommunityTab>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          // Tab Bar
          Container(
            decoration: const BoxDecoration(color: Colors.white),
            child: Tab<PERSON><PERSON>(
              controller: _tabController,
              labelColor: const Color(0xFF2E7D32),
              unselectedLabelColor: Colors.grey,
              indicatorColor: const Color(0xFF2E7D32),
              tabs: const [
                Tab(text: 'For You'),
                Tab(text: 'Following'),
                Tab(text: 'Communities'),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: Tab<PERSON><PERSON><PERSON>iew(
              controller: _tabController,
              children: [
                _buildForYouTab(),
                _buildFollowingTab(),
                _buildCommunitiesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForYouTab() {
    return RefreshIndicator(
      onRefresh: () async {
        // TODO: Implement refresh logic
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _generateSamplePosts().length,
        itemBuilder: (context, index) {
          final post = _generateSamplePosts()[index];
          return _buildPostCard(post);
        },
      ),
    );
  }

  Widget _buildFollowingTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 80, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No posts from followed users',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Follow other users to see their posts here',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildCommunitiesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _generateSampleCommunities().length,
      itemBuilder: (context, index) {
        final community = _generateSampleCommunities()[index];
        return _buildCommunityCard(community);
      },
    );
  }

  Widget _buildPostCard(Map<String, dynamic> post) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Post Header
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: const Color(0xFF2E7D32),
                  child: Text(
                    post['author'][0].toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post['author'],
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        _formatPostTime(post['timestamp']),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () => _showPostOptions(post),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Post Content
            Text(
              post['title'],
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(post['content'], style: const TextStyle(fontSize: 16)),

            // Post Image (if any)
            if (post['hasImage'])
              Container(
                margin: const EdgeInsets.only(top: 12),
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Icon(Icons.image, size: 50, color: Colors.grey),
                ),
              ),

            const SizedBox(height: 12),

            // Post Actions
            Row(
              children: [
                _buildPostAction(
                  icon: post['isUpvoted']
                      ? Icons.thumb_up
                      : Icons.thumb_up_outlined,
                  label: post['upvotes'].toString(),
                  color: post['isUpvoted']
                      ? const Color(0xFF2E7D32)
                      : Colors.grey,
                  onTap: () => _toggleUpvote(post),
                ),
                const SizedBox(width: 24),
                _buildPostAction(
                  icon: Icons.comment_outlined,
                  label: post['comments'].toString(),
                  color: Colors.grey,
                  onTap: () => _openComments(post),
                ),
                const SizedBox(width: 24),
                _buildPostAction(
                  icon: Icons.share_outlined,
                  label: 'Share',
                  color: Colors.grey,
                  onTap: () => _sharePost(post),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    post['isSaved'] ? Icons.bookmark : Icons.bookmark_border,
                    color: post['isSaved']
                        ? const Color(0xFF2E7D32)
                        : Colors.grey,
                  ),
                  onPressed: () => _toggleSave(post),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostAction({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 4),
          Text(label, style: TextStyle(color: color, fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildCommunityCard(Map<String, dynamic> community) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          radius: 25,
          backgroundColor: community['color'],
          child: Icon(community['icon'], color: Colors.white, size: 24),
        ),
        title: Text(
          community['name'],
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(community['description']),
            const SizedBox(height: 4),
            Text(
              '${community['members']} members • ${community['posts']} posts',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
          ],
        ),
        trailing: ElevatedButton(
          onPressed: () => _joinCommunity(community),
          style: ElevatedButton.styleFrom(
            backgroundColor: community['isJoined']
                ? Colors.grey.shade300
                : const Color(0xFF2E7D32),
            foregroundColor: community['isJoined']
                ? Colors.grey.shade700
                : Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Text(community['isJoined'] ? 'Joined' : 'Join'),
        ),
        onTap: () => _openCommunity(community),
      ),
    );
  }

  List<Map<String, dynamic>> _generateSamplePosts() {
    return [
      {
        'id': '1',
        'author': 'John Doe',
        'title': 'Looking for Study Group - Engineering Mathematics',
        'content':
            'Anyone interested in forming a study group for Engineering Mathematics? We can meet at the library every Tuesday and Thursday.',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'upvotes': 15,
        'comments': 8,
        'isUpvoted': false,
        'isSaved': false,
        'hasImage': false,
      },
      {
        'id': '2',
        'author': 'Jane Smith',
        'title': 'Free Lab Equipment Available',
        'content':
            'I have some extra lab equipment that I no longer need. First come, first served! Contact me if interested.',
        'timestamp': DateTime.now().subtract(const Duration(hours: 5)),
        'upvotes': 23,
        'comments': 12,
        'isUpvoted': true,
        'isSaved': true,
        'hasImage': true,
      },
      {
        'id': '3',
        'author': 'Mike Johnson',
        'title': 'Campus Event: Tech Talk Tomorrow',
        'content':
            'Don\'t miss the tech talk tomorrow at 2 PM in the main auditorium. Industry experts will be discussing the latest trends in AI.',
        'timestamp': DateTime.now().subtract(const Duration(days: 1)),
        'upvotes': 45,
        'comments': 20,
        'isUpvoted': false,
        'isSaved': false,
        'hasImage': false,
      },
    ];
  }

  List<Map<String, dynamic>> _generateSampleCommunities() {
    return [
      {
        'id': '1',
        'name': 'Engineering Club',
        'description':
            'For all engineering students to share knowledge and collaborate',
        'members': 245,
        'posts': 89,
        'icon': Icons.engineering,
        'color': Colors.blue,
        'isJoined': true,
      },
      {
        'id': '2',
        'name': 'Dorm Deals',
        'description': 'Buy, sell, and trade items within the dormitories',
        'members': 156,
        'posts': 234,
        'icon': Icons.home,
        'color': Colors.orange,
        'isJoined': false,
      },
      {
        'id': '3',
        'name': 'Study Buddies',
        'description': 'Find study partners and form study groups',
        'members': 189,
        'posts': 67,
        'icon': Icons.school,
        'color': Colors.purple,
        'isJoined': true,
      },
      {
        'id': '4',
        'name': 'Sports & Recreation',
        'description': 'Organize sports activities and recreational events',
        'members': 98,
        'posts': 45,
        'icon': Icons.sports_basketball,
        'color': Colors.green,
        'isJoined': false,
      },
    ];
  }

  String _formatPostTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM dd').format(time);
    }
  }

  void _toggleUpvote(Map<String, dynamic> post) {
    setState(() {
      if (post['isUpvoted']) {
        post['upvotes']--;
        post['isUpvoted'] = false;
      } else {
        post['upvotes']++;
        post['isUpvoted'] = true;
      }
    });
  }

  void _toggleSave(Map<String, dynamic> post) {
    setState(() {
      post['isSaved'] = !post['isSaved'];
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(post['isSaved'] ? 'Post saved' : 'Post unsaved'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _openComments(Map<String, dynamic> post) {
    // TODO: Navigate to comments screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Comments coming soon!'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _sharePost(Map<String, dynamic> post) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showPostOptions(Map<String, dynamic> post) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.flag),
              title: const Text('Report Post'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Report post
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Hide Post'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Hide post
              },
            ),
          ],
        ),
      ),
    );
  }

  void _joinCommunity(Map<String, dynamic> community) {
    setState(() {
      community['isJoined'] = !community['isJoined'];
      if (community['isJoined']) {
        community['members']++;
      } else {
        community['members']--;
      }
    });
  }

  void _openCommunity(Map<String, dynamic> community) {
    // TODO: Navigate to community details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${community['name']}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
